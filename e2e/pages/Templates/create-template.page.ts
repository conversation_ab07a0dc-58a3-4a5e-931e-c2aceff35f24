import { Page, Locator, expect } from '@playwright/test';

export class CreateTemplatePage {
  readonly page: Page;
  readonly nameInput: Locator;
  readonly descriptionInput: Locator;
  readonly categorySelect: Locator;
  readonly saveButton: Locator;
  readonly addQuestionButton: Locator;
  readonly questionCards: Locator;
  readonly successAlert: Locator;
  readonly errorAlert: Locator;
  readonly loadingSpinner: Locator;

  constructor(page: Page) {
    this.page = page;
    this.nameInput = page.locator('input[name="name"], input[label*="Template Name"]');
    this.descriptionInput = page.locator('textarea[name="description"], textarea[label*="Description"]');
    this.categorySelect = page.locator('select[name="category"], [data-testid="category-select"]');
    this.saveButton = page.locator('button[type="submit"], button:has-text("Save"), button:has-text("Create")');
    this.addQuestionButton = page.locator('button:has-text("Add Question")');
    this.questionCards = page.locator('[data-testid="question-card"]');
    this.successAlert = page.locator('.MuiAlert-root:has-text("success"), .MuiAlert-root:has-text("created")');
    this.errorAlert = page.locator('.MuiAlert-root:has-text("error"), .MuiAlert-root:has-text("failed")');
    this.loadingSpinner = page.locator('.MuiCircularProgress-root');
  }

  async goto() {
    await this.page.goto('/trq/templates/add');
    await expect(this.page).toHaveURL('/trq/templates/add');
    await this.waitForPageLoad();
  }

  async gotoEdit(templateId: string) {
    await this.page.goto(`/trq/templates/${templateId}/edit`);
    await expect(this.page).toHaveURL(`/trq/templates/${templateId}/edit`);
    await this.waitForPageLoad();
  }

  async waitForPageLoad() {
    await expect(this.nameInput).toBeVisible({ timeout: 15000 });
    await this.page.waitForLoadState('networkidle');
  }

  async fillBasicDetails(name: string, description: string, category?: string) {
    await expect(this.nameInput).toBeVisible();
    await this.nameInput.fill(name);
    
    await expect(this.descriptionInput).toBeVisible();
    await this.descriptionInput.fill(description);
    
    if (category) {
      await this.selectCategory(category);
    }
    
    // Wait for form validation
    await this.page.waitForTimeout(500);
  }

  async selectCategory(category: string) {
    await expect(this.categorySelect).toBeVisible();
    await this.categorySelect.click();
    await this.page.locator(`option:has-text("${category}"), [role="option"]:has-text("${category}")`).click();
  }

  async addQuestion(questionText: string, questionType: string = 'TEXT') {
    await expect(this.addQuestionButton).toBeVisible();
    await this.addQuestionButton.click();
    
    // Wait for the new question form to appear
    await this.page.waitForTimeout(1000);
    
    // Fill in the question text (assuming the last question card is the new one)
    const questionCards = await this.questionCards.all();
    const lastQuestionCard = questionCards[questionCards.length - 1];
    
    const questionTextInput = lastQuestionCard.locator('input[name*="text"], textarea[name*="text"]');
    await expect(questionTextInput).toBeVisible();
    await questionTextInput.fill(questionText);
    
    // Select question type if needed
    if (questionType !== 'TEXT') {
      const questionTypeSelect = lastQuestionCard.locator('select[name*="questionType"], [data-testid*="question-type"]');
      await questionTypeSelect.click();
      await this.page.locator(`option:has-text("${questionType}"), [role="option"]:has-text("${questionType}")`).click();
    }
    
    await this.page.waitForTimeout(500);
  }

  async save() {
    await expect(this.saveButton).toBeVisible();
    await expect(this.saveButton).toBeEnabled();
    await this.saveButton.click();
  }

  async verifySuccess() {
    await expect(this.successAlert).toBeVisible({ timeout: 15000 });
    // Wait for navigation back to templates list
    await this.page.waitForTimeout(2000);
    await expect(this.page).toHaveURL('/trq/templates', { timeout: 10000 });
  }

  async verifyError() {
    await expect(this.errorAlert).toBeVisible({ timeout: 10000 });
  }

  async waitForSaveComplete() {
    // Wait for any loading spinner to disappear
    try {
      await expect(this.loadingSpinner).toBeVisible({ timeout: 2000 });
      await expect(this.loadingSpinner).not.toBeVisible({ timeout: 15000 });
    } catch {
      // No loading spinner found, continue
    }
    
    // Additional wait for any async operations
    await this.page.waitForTimeout(1000);
  }

  async getQuestionsCount(): Promise<number> {
    try {
      return await this.questionCards.count();
    } catch {
      return 0;
    }
  }

  async deleteQuestion(index: number) {
    const questionCards = await this.questionCards.all();
    if (index >= questionCards.length) {
      throw new Error(`Question index ${index} is out of bounds`);
    }
    
    const questionCard = questionCards[index];
    const deleteButton = questionCard.locator('button[aria-label="Delete"], button:has([data-testid="DeleteIcon"])');
    await expect(deleteButton).toBeVisible();
    await deleteButton.click();
    
    // Confirm deletion if there's a confirmation dialog
    try {
      const confirmButton = this.page.locator('[role="dialog"] button:has-text("Delete"), [role="dialog"] button:has-text("Confirm")');
      await expect(confirmButton).toBeVisible({ timeout: 2000 });
      await confirmButton.click();
    } catch {
      // No confirmation dialog, continue
    }
    
    await this.page.waitForTimeout(500);
  }

  async moveQuestionUp(index: number) {
    const questionCards = await this.questionCards.all();
    if (index >= questionCards.length || index === 0) {
      throw new Error(`Cannot move question at index ${index} up`);
    }
    
    const questionCard = questionCards[index];
    const moveUpButton = questionCard.locator('button[aria-label="Move Up"], button:has([data-testid="ArrowUpwardIcon"])');
    await expect(moveUpButton).toBeVisible();
    await moveUpButton.click();
    await this.page.waitForTimeout(500);
  }

  async moveQuestionDown(index: number) {
    const questionCards = await this.questionCards.all();
    if (index >= questionCards.length - 1) {
      throw new Error(`Cannot move question at index ${index} down`);
    }
    
    const questionCard = questionCards[index];
    const moveDownButton = questionCard.locator('button[aria-label="Move Down"], button:has([data-testid="ArrowDownwardIcon"])');
    await expect(moveDownButton).toBeVisible();
    await moveDownButton.click();
    await this.page.waitForTimeout(500);
  }
}
