import { Page, Locator, expect } from '@playwright/test';

export class AllTemplatesPage {
  readonly page: Page;
  readonly createTemplateButton: Locator;
  readonly searchInput: Locator;
  readonly categoryFilter: Locator;
  readonly templateCards: Locator;
  readonly templateTitles: Locator;
  readonly editButtons: Locator;
  readonly deleteButtons: Locator;
  readonly publishedChips: Locator;
  readonly draftChips: Locator;

  constructor(page: Page) {
    this.page = page;
    this.createTemplateButton = page.locator('button:has-text("Create Template")');
    this.searchInput = page.locator('input[placeholder*="Search templates"]');
    this.categoryFilter = page.locator('[data-testid="category-filter"]');
    this.templateCards = page.locator('[data-testid="template-card"]');
    this.templateTitles = page.locator('[data-testid="template-title"]');
    this.editButtons = page.locator('button[aria-label="Edit"], button:has([data-testid="EditIcon"])');
    this.deleteButtons = page.locator('button[aria-label="Delete"], button:has([data-testid="DeleteIcon"])');
    this.publishedChips = page.locator('.MuiChip-root:has-text("Published")');
    this.draftChips = page.locator('.MuiChip-root:has-text("Draft")');
  }

  async goto() {
    await this.page.goto('/trq/templates');
    await expect(this.page).toHaveURL('/trq/templates');
    await this.waitForPageLoad();
  }

  async waitForPageLoad() {
    // Wait for either template cards to load or empty state
    await Promise.race([
      this.page.waitForSelector('[data-testid="template-card"]', { state: 'visible', timeout: 15000 }),
      this.page.waitForSelector('text=No templates found', { state: 'visible', timeout: 15000 }),
      this.page.waitForSelector('.MuiGrid-container', { state: 'visible', timeout: 15000 })
    ]);
    await this.page.waitForLoadState('networkidle');
  }

  async clickCreateTemplate() {
    await expect(this.createTemplateButton).toBeVisible();
    await this.createTemplateButton.click();
    await expect(this.page).toHaveURL('/trq/templates/add');
  }

  async searchTemplates(searchTerm: string) {
    await this.searchInput.fill(searchTerm);
    await this.page.waitForTimeout(1000); // Wait for search to process
  }

  async selectCategoryFilter(category: string) {
    await this.categoryFilter.click();
    await this.page.locator(`text="${category}"`).click();
    await this.page.waitForTimeout(1000); // Wait for filter to apply
  }

  async getTemplateCount(): Promise<number> {
    return await this.templateCards.count();
  }

  async getTemplateByTitle(title: string): Promise<Locator> {
    return this.page.locator(`[data-testid="template-card"]:has-text("${title}")`);
  }

  async clickTemplateByTitle(title: string) {
    const templateCard = await this.getTemplateByTitle(title);
    await expect(templateCard).toBeVisible();
    await templateCard.click();
  }

  async editTemplateByTitle(title: string) {
    const templateCard = await this.getTemplateByTitle(title);
    await expect(templateCard).toBeVisible();
    
    // Click the edit button within the template card
    const editButton = templateCard.locator('button[aria-label="Edit"], button:has([data-testid="EditIcon"])');
    await expect(editButton).toBeVisible();
    await editButton.click();
  }

  async deleteTemplateByTitle(title: string) {
    const templateCard = await this.getTemplateByTitle(title);
    await expect(templateCard).toBeVisible();
    
    // Click the delete button within the template card
    const deleteButton = templateCard.locator('button[aria-label="Delete"], button:has([data-testid="DeleteIcon"])');
    await expect(deleteButton).toBeVisible();
    await deleteButton.click();
  }

  async verifyTemplateExists(title: string): Promise<boolean> {
    try {
      const templateCard = await this.getTemplateByTitle(title);
      await expect(templateCard).toBeVisible({ timeout: 5000 });
      return true;
    } catch {
      return false;
    }
  }

  async verifyTemplatePublished(title: string): Promise<boolean> {
    const templateCard = await this.getTemplateByTitle(title);
    const publishedChip = templateCard.locator('.MuiChip-root:has-text("Published")');
    try {
      await expect(publishedChip).toBeVisible({ timeout: 5000 });
      return true;
    } catch {
      return false;
    }
  }

  async verifyTemplateDraft(title: string): Promise<boolean> {
    const templateCard = await this.getTemplateByTitle(title);
    const draftChip = templateCard.locator('.MuiChip-root:has-text("Draft")');
    try {
      await expect(draftChip).toBeVisible({ timeout: 5000 });
      return true;
    } catch {
      return false;
    }
  }

  async getPublishedTemplatesCount(): Promise<number> {
    return await this.publishedChips.count();
  }

  async getDraftTemplatesCount(): Promise<number> {
    return await this.draftChips.count();
  }
}
