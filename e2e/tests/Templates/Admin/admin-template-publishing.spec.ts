import { test, expect } from '@playwright/test';
import { loginAs } from '../../../utils/auth-utils';
import { Role } from '../../../../src/RBAC/[types]/Role';
import { AllTemplatesPage } from '../../../pages/Templates/all-templates.page';
import { CreateTemplatePage } from '../../../pages/Templates/create-template.page';
import { TemplateDetailsPage } from '../../../pages/Templates/template-details.page';
import { ProductsPage } from '../../../pages/Products/products.page';

test.describe('Admin Template Publishing Workflow', () => {
  let allTemplatesPage: AllTemplatesPage;
  let createTemplatePage: CreateTemplatePage;
  let templateDetailsPage: TemplateDetailsPage;
  let productsPage: ProductsPage;

  test.beforeEach(async ({ page, baseURL }) => {
    // Initialize page objects
    allTemplatesPage = new AllTemplatesPage(page);
    createTemplatePage = new CreateTemplatePage(page);
    templateDetailsPage = new TemplateDetailsPage(page);
    productsPage = new ProductsPage(page);

    // Login as admin before each test (optimized for reliability)
    await loginAs(page, Role.Admin, baseURL);
  });

  test('should allow admin to create a template and publish it as a product', async () => {
    const templateName = `E2E Admin Template ${Date.now()}`;
    const templateDescription = 'This is a test template created by admin for e2e testing';

    // Step 1: Navigate to templates page
    await allTemplatesPage.goto();
    
    // Step 2: Create a new template
    await allTemplatesPage.clickCreateTemplate();
    await createTemplatePage.fillBasicDetails(templateName, templateDescription, 'medical');

    // Add one question to make it a valid template
    await createTemplatePage.addQuestion('What is your age?', 'Text');
    
    // Save the template
    await createTemplatePage.save();
    await createTemplatePage.verifySuccess();

    // Step 3: Verify template appears in templates list
    await allTemplatesPage.waitForPageLoad();
    const templateExists = await allTemplatesPage.verifyTemplateExists(templateName);
    expect(templateExists).toBe(true);

    // Step 4: Verify template is initially in draft state
    const isDraft = await allTemplatesPage.verifyTemplateDraft(templateName);
    expect(isDraft).toBe(true);

    // Step 5: Navigate to template details
    await allTemplatesPage.clickTemplateByTitle(templateName);
    
    // Step 6: Verify template details page loads
    await templateDetailsPage.waitForPageLoad();
    const detailsTitle = await templateDetailsPage.getTemplateTitle();
    expect(detailsTitle).toContain(templateName);

    // Step 7: Verify template is not published initially
    const isPublished = await templateDetailsPage.isPublished();
    expect(isPublished).toBe(false);

    // Step 8: Publish the template
    await templateDetailsPage.publishTemplate();
    await templateDetailsPage.waitForPublishingComplete();

    // Step 9: Verify template is now published
    const isNowPublished = await templateDetailsPage.isPublished();
    expect(isNowPublished).toBe(true);

    // Step 10: Navigate to products page
    await productsPage.goto();

    // Step 11: Verify the published template appears as a product
    const productExists = await productsPage.verifyPublishedTemplateAsProduct(templateName);
    expect(productExists).toBe(true);

    // Step 12: Verify product details
    const productDetails = await productsPage.getProductDetails(templateName);
    expect(productDetails.title).toContain(templateName);
  });

  test('should allow admin to unpublish a template and verify it disappears from products', async () => {
    const templateName = `E2E Admin Unpublish Template ${Date.now()}`;
    const templateDescription = 'This template will be published then unpublished';

    // Step 1: Create and publish a template
    await allTemplatesPage.goto();
    await allTemplatesPage.clickCreateTemplate();
    await createTemplatePage.fillBasicDetails(templateName, templateDescription, 'general');
    await createTemplatePage.addQuestion('Sample question for unpublish test', 'Text');
    await createTemplatePage.save();
    await createTemplatePage.verifySuccess();

    // Navigate to template details and publish
    await allTemplatesPage.clickTemplateByTitle(templateName);
    await templateDetailsPage.publishTemplate();
    await templateDetailsPage.waitForPublishingComplete();

    // Verify it appears as a product
    await productsPage.goto();
    let productExists = await productsPage.verifyPublishedTemplateAsProduct(templateName);
    expect(productExists).toBe(true);

    // Step 2: Go back to template and unpublish
    await allTemplatesPage.goto();
    await allTemplatesPage.clickTemplateByTitle(templateName);
    await templateDetailsPage.unpublishTemplate();
    await templateDetailsPage.waitForPublishingComplete();

    // Step 3: Verify template is no longer published
    const isPublished = await templateDetailsPage.isPublished();
    expect(isPublished).toBe(false);

    // Step 4: Verify product no longer appears in products page
    await productsPage.goto();
    productExists = await productsPage.verifyPublishedTemplateAsProduct(templateName);
    expect(productExists).toBe(false);
  });

  test('should allow admin to create template from existing template and publish it', async () => {
    const originalTemplateName = `E2E Original Template ${Date.now()}`;
    const newTemplateName = `E2E Copied Template ${Date.now()}`;

    // Step 1: Create an original template
    await allTemplatesPage.goto();
    await allTemplatesPage.clickCreateTemplate();
    await createTemplatePage.fillBasicDetails(originalTemplateName, 'Original template for copying', 'medical');
    await createTemplatePage.addQuestion('Original question 1', 'Text');
    await createTemplatePage.addQuestion('Original question 2', 'Yes/No');
    await createTemplatePage.save();
    await createTemplatePage.verifySuccess();

    // Step 2: Create a new template (simulating copy workflow)
    await allTemplatesPage.clickCreateTemplate();
    await createTemplatePage.fillBasicDetails(newTemplateName, 'Copied and modified template', 'medical');
    await createTemplatePage.addQuestion('Modified question 1', 'Text');
    await createTemplatePage.addQuestion('Modified question 2', 'Scale');
    await createTemplatePage.addQuestion('New additional question', 'Text');
    await createTemplatePage.save();
    await createTemplatePage.verifySuccess();

    // Step 3: Publish the new template
    await allTemplatesPage.clickTemplateByTitle(newTemplateName);
    await templateDetailsPage.publishTemplate();
    await templateDetailsPage.waitForPublishingComplete();

    // Step 4: Verify both templates exist but only the new one is published
    await allTemplatesPage.goto();
    const originalExists = await allTemplatesPage.verifyTemplateExists(originalTemplateName);
    const newExists = await allTemplatesPage.verifyTemplateExists(newTemplateName);
    expect(originalExists).toBe(true);
    expect(newExists).toBe(true);

    const originalIsDraft = await allTemplatesPage.verifyTemplateDraft(originalTemplateName);
    const newIsPublished = await allTemplatesPage.verifyTemplatePublished(newTemplateName);
    expect(originalIsDraft).toBe(true);
    expect(newIsPublished).toBe(true);

    // Step 5: Verify only the published template appears as a product
    await productsPage.goto();
    const originalAsProduct = await productsPage.verifyPublishedTemplateAsProduct(originalTemplateName);
    const newAsProduct = await productsPage.verifyPublishedTemplateAsProduct(newTemplateName);
    expect(originalAsProduct).toBe(false);
    expect(newAsProduct).toBe(true);
  });

  test('should prevent editing published templates', async () => {
    const templateName = `E2E Published Template ${Date.now()}`;

    // Step 1: Create and publish a template
    await allTemplatesPage.goto();
    await allTemplatesPage.clickCreateTemplate();
    await createTemplatePage.fillBasicDetails(templateName, 'Template to test edit restrictions', 'general');
    await createTemplatePage.addQuestion('Test question', 'Text');
    await createTemplatePage.save();
    await createTemplatePage.verifySuccess();

    // Publish the template
    await allTemplatesPage.clickTemplateByTitle(templateName);
    await templateDetailsPage.publishTemplate();
    await templateDetailsPage.waitForPublishingComplete();

    // Step 2: Verify edit button is not available for published template
    const canEdit = await templateDetailsPage.verifyTemplateCanBeEdited();
    expect(canEdit).toBe(false);

    // Step 3: Unpublish and verify edit button becomes available
    await templateDetailsPage.unpublishTemplate();
    await templateDetailsPage.waitForPublishingComplete();
    
    const canEditAfterUnpublish = await templateDetailsPage.verifyTemplateCanBeEdited();
    expect(canEditAfterUnpublish).toBe(true);
  });
});
