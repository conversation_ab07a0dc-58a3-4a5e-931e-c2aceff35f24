import { 
  collection, 
  doc, 
  getDocs, 
  query, 
  where, 
  updateDoc, 
  deleteDoc,
  getDoc
} from 'firebase/firestore';
import { db } from '../../firebase/[config]/firebase';
import { createProduct, Product } from './productsService';
import { getTemplateById, getTemplates, QuestionnaireTemplate } from '../../Questionnaires/Templates/[services]/questionnaireTemplateService';

const productsCollection = collection(db, 'products');

/**
 * Find existing product for a template
 */
export const getProductByTemplateId = async (templateId: string): Promise<Product | null> => {
  try {
    const q = query(productsCollection, where('questionnaireId', '==', templateId));
    const querySnapshot = await getDocs(q);
    
    if (querySnapshot.empty) {
      return null;
    }
    
    const productDoc = querySnapshot.docs[0];
    return {
      id: productDoc.id,
      ...productDoc.data()
    } as Product;
  } catch (error) {
    console.error('Error finding product by template ID:', error);
    throw error;
  }
};

/**
 * Create a product when template is published
 */
export const syncProductOnTemplatePublish = async (templateId: string): Promise<Product> => {
  try {
    console.log(`Syncing product for template: ${templateId}`);
    
    // Check if product already exists
    const existingProduct = await getProductByTemplateId(templateId);
    if (existingProduct) {
      console.log(`Product already exists for template ${templateId}:`, existingProduct);
      // Product exists, make sure it's active
      const productRef = doc(productsCollection, existingProduct.id);
      await updateDoc(productRef, {
        isActive: true,
        updatedAt: new Date()
      });
      console.log(`Activated existing product for template ${templateId}`);
      return { ...existingProduct, isActive: true };
    }

    // Get template details
    console.log(`Fetching template details for ${templateId}`);
    const template = await getTemplateById(templateId);
    if (!template) {
      throw new Error(`Template with ID ${templateId} not found`);
    }
    
    console.log(`Template details:`, {
      id: template.id,
      name: template.name,
      description: template.description,
      isPublished: template.isPublished,
      coverImage: template.coverImage,
      category: template.metadata?.category,
      tags: template.tags
    });

    // Create new product from template
    const productData = {
      name: template.name || 'Untitled Product',
      description: template.description || '',
      price: 0, // Default price, can be updated later
      questionnaireId: templateId,
      imageUrl: template.coverImage || '',
      isActive: true,
      category: template.metadata?.category || 'General',
      tags: template.tags || [],
      createdAt: new Date(),
      updatedAt: new Date()
    };

    console.log(`Creating product with data:`, productData);
    const newProduct = await createProduct(db, productData);
    console.log(`Product created successfully:`, newProduct);
    return newProduct;
  } catch (error) {
    console.error('Error syncing product on template publish:', error);
    throw error;
  }
};

/**
 * Hide/deactivate product when template is unpublished
 */
export const syncProductOnTemplateUnpublish = async (templateId: string): Promise<void> => {
  try {
    const existingProduct = await getProductByTemplateId(templateId);
    if (!existingProduct) {
      // No product exists, nothing to do
      return;
    }

    // Deactivate the product instead of deleting it
    const productRef = doc(productsCollection, existingProduct.id);
    await updateDoc(productRef, {
      isActive: false,
      updatedAt: new Date()
    });
  } catch (error) {
    console.error('Error syncing product on template unpublish:', error);
    throw error;
  }
};

/**
 * Update product details from template changes
 */
export const updateProductFromTemplate = async (templateId: string): Promise<void> => {
  try {
    const existingProduct = await getProductByTemplateId(templateId);
    if (!existingProduct) {
      return; // No product to update
    }

    const template = await getTemplateById(templateId);
    if (!template) {
      return; // Template doesn't exist
    }

    const productRef = doc(productsCollection, existingProduct.id);
    await updateDoc(productRef, {
      name: template.name || existingProduct.name,
      description: template.description || existingProduct.description,
      imageUrl: template.coverImage || existingProduct.imageUrl,
      category: template.metadata?.category || existingProduct.category,
      tags: template.tags || existingProduct.tags,
      updatedAt: new Date()
    });
  } catch (error) {
    console.error('Error updating product from template:', error);
    throw error;
  }
};

/**
 * Get all products linked to published templates only
 */
export const getPublishedProducts = async (): Promise<Product[]> => {
  try {
    console.log('Getting published products...');
    
    // Get all active products
    const q = query(productsCollection, where('isActive', '==', true));
    const querySnapshot = await getDocs(q);
    
    console.log(`Found ${querySnapshot.docs.length} active products`);
    
    const products: Product[] = [];
    
    for (const doc of querySnapshot.docs) {
      const productData = { id: doc.id, ...doc.data() } as Product;
      console.log(`Processing product: ${productData.name} (${productData.id}) - questionnaireId: ${productData.questionnaireId}`);
      
      // If product has a questionnaireId, verify the template is published
      if (productData.questionnaireId) {
        try {
          const template = await getTemplateById(productData.questionnaireId);
          console.log(`Template for product ${productData.name}: ${template ? `Found (Published: ${template.isPublished})` : 'Not found'}`);
          
          if (template && template.isPublished) {
            products.push(productData);
            console.log(`✅ Including product: ${productData.name}`);
          } else {
            console.log(`❌ Excluding product: ${productData.name} - Template not published or not found`);
          }
        } catch (error) {
          // Template might not exist, skip this product
          console.warn(`Template ${productData.questionnaireId} not found for product ${productData.id}:`, error);
        }
      } else {
        // Product without template link, include it
        products.push(productData);
        console.log(`✅ Including product without template: ${productData.name}`);
      }
    }
    
    console.log(`Returning ${products.length} published products`);
    return products;
  } catch (error) {
    console.error('Error getting published products:', error);
    throw error;
  }
};

/**
 * Migration function: Create products for all existing published templates
 * Call this once to sync existing published templates with products
 */
export const syncAllPublishedTemplates = async (): Promise<{ created: number; errors: string[] }> => {
  try {
    console.log('Starting migration: syncing all published templates with products...');
    
    const allTemplates = await getTemplates();
    console.log(`Found ${allTemplates.length} total templates`);
    
    const publishedTemplates = allTemplates.filter(template => template.isPublished);
    console.log(`Found ${publishedTemplates.length} published templates:`);
    publishedTemplates.forEach(template => {
      console.log(`- ${template.name} (${template.id}) - Published: ${template.isPublished}`);
    });
    
    let created = 0;
    const errors: string[] = [];
    
    for (const template of publishedTemplates) {
      try {
        console.log(`\nProcessing template: ${template.name} (${template.id})`);
        
        // Check if product already exists
        const existingProduct = await getProductByTemplateId(template.id);
        console.log(`Existing product found: ${existingProduct ? 'Yes' : 'No'}`);
        
        if (!existingProduct) {
          // Create new product
          console.log(`Creating product for template: ${template.name}`);
          const newProduct = await syncProductOnTemplatePublish(template.id);
          console.log(`Product created successfully:`, newProduct);
          created++;
        } else {
          console.log(`Existing product details:`, existingProduct);
          // Ensure existing product is active
          if (existingProduct.isActive === false) {
            const productRef = doc(productsCollection, existingProduct.id);
            await updateDoc(productRef, { isActive: true, updatedAt: new Date() });
            console.log(`Activated existing product for template: ${template.name}`);
          } else {
            console.log(`Product already exists and is active for template: ${template.name}`);
          }
        }
      } catch (error) {
        const errorMsg = `Failed to sync template ${template.name} (${template.id}): ${error}`;
        console.error(errorMsg, error);
        errors.push(errorMsg);
      }
    }
    
    console.log(`\nMigration completed: ${created} products created, ${errors.length} errors`);
    return { created, errors };
  } catch (error) {
    console.error('Error in syncAllPublishedTemplates:', error);
    throw error;
  }
};