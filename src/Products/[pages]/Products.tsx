import { useEffect, useState, useMemo, useCallback } from 'react';
import ProductCard from '../[components]/ProductCard';
import {
  Container,
  Box,
  Grid,
  Typography,
  TextField,
  Divider,
  Card,
  CardContent,
  InputAdornment,
  FormControl,
  Select,
  MenuItem,
  Chip,
  Stack,
  Pagination,
  ToggleButton,
  ToggleButtonGroup,
  Skeleton,
  Paper,
  Button,
  Tooltip,
  IconButton,
  Menu,
  Popover,
  Slider,
  Badge,
  FormControlLabel,
  Checkbox,
  useTheme,
  useMediaQuery,
  SelectChangeEvent
} from '@mui/material';

import { getProducts } from '../[services]/productsService';
import { getPublishedProducts, syncAllPublishedTemplates } from '../[services]/templateProductSyncService';
import { Product } from '../[services]/productsService';

import SearchIcon from '@mui/icons-material/Search';
import GridViewIcon from '@mui/icons-material/GridView';
import ViewListIcon from '@mui/icons-material/ViewList';
import SortIcon from '@mui/icons-material/Sort';
import TuneIcon from '@mui/icons-material/Tune';
import RefreshIcon from '@mui/icons-material/Refresh';
import NewReleasesIcon from '@mui/icons-material/NewReleases';
import LocalOfferIcon from '@mui/icons-material/LocalOffer';

const Products = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Component state
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('default');

  // Filter state
  const [categories, setCategories] = useState<string[]>([]);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 1000]);
  const [priceRangeBounds, setPriceRangeBounds] = useState<[number, number]>([0, 1000]);
  const [showNewOnly, setShowNewOnly] = useState(false);
  const [showDiscountsOnly, setShowDiscountsOnly] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 8;

  // Migration state
  const [isMigrating, setIsMigrating] = useState(false);
  const [migrationResult, setMigrationResult] = useState<{ created: number; errors: string[] } | null>(null);

  // Filter popover state
  const [filterAnchorEl, setFilterAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [sortAnchorEl, setSortAnchorEl] = useState<HTMLButtonElement | null>(null);
  const openFilterPopover = Boolean(filterAnchorEl);
  const openSortMenu = Boolean(sortAnchorEl);

  // Reset all filters to their initial state
  const resetFilters = useCallback(() => {
    setSearchTerm('');
    setSelectedCategories([]);
    setPriceRange(priceRangeBounds);
    setShowNewOnly(false);
    setShowDiscountsOnly(false);
    setSortBy('default');
    setCurrentPage(1);
  }, [priceRangeBounds]);

  // Filter, sort, and paginate products
  const filteredProducts = useMemo(() => {
    return products.filter((product) => {
      // Search term filter
      const searchMatch =
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.description.toLowerCase().includes(searchTerm.toLowerCase());
      // Category filter
      const categoryMatch = selectedCategories.length === 0 || (product.category && selectedCategories.includes(product.category));
      // Price range filter
      const priceMatch = (product.price || 0) >= priceRange[0] && (product.price || 0) <= priceRange[1];
      // New arrivals filter
      const newOnlyMatch = !showNewOnly || product.isNew;
      // Discounts filter
      const discountsOnlyMatch = !showDiscountsOnly || (product.originalPrice && product.originalPrice > (product.price || 0));
      return searchMatch && categoryMatch && priceMatch && newOnlyMatch && discountsOnlyMatch;
    });
  }, [products, searchTerm, selectedCategories, priceRange, showNewOnly, showDiscountsOnly]);
  const sortedProducts = useMemo(() => {
    const sortable = [...filteredProducts];
    sortable.sort((a, b) => {
      switch (sortBy) {
        case 'price-low':
          return (a.price || 0) - (b.price || 0);
        case 'price-high':
          return (b.price || 0) - (a.price || 0);
        case 'name-asc':
          return a.name.localeCompare(b.name);
        case 'name-desc':
          return b.name.localeCompare(a.name);
        case 'rating-high':
          return (b.rating || 0) - (a.rating || 0);
        case 'newest':
          return (b.createdAt?.getTime() || 0) - (a.createdAt?.getTime() || 0);
        default:
          return 0;
      }
    });
    return sortable;
  }, [filteredProducts, sortBy]);

  const paginatedProducts = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return sortedProducts.slice(startIndex, startIndex + itemsPerPage);
  }, [sortedProducts, currentPage, itemsPerPage]);

  // Load products and initialize filters
  useEffect(() => {
    const fetchProducts = async () => {
      setLoading(true);
      try {
        const data = await getPublishedProducts();
        const processedData = data.map((p) => ({ ...p, createdAt: p.createdAt ? new Date(p.createdAt) : new Date() }));
        setProducts(processedData);

        // Initialize categories and price range from data
        if (processedData.length > 0) {
          const uniqueCategories = [...new Set(processedData.map((p) => p.category).filter(Boolean) as string[])];
          setCategories(uniqueCategories);

          const prices = processedData.map((p) => p.price || 0).filter(Boolean);
          if (prices.length > 0) {
            const min = Math.floor(Math.min(...prices));
            const max = Math.ceil(Math.max(...prices));
            setPriceRange([min, max]);
            setPriceRangeBounds([min, max]);
          }
        }
      } catch (error) {
        console.error('Error fetching products:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  // Handlers
  const handleFilterClick = (event: React.MouseEvent<HTMLButtonElement>) => setFilterAnchorEl(event.currentTarget);
  const handleFilterClose = () => setFilterAnchorEl(null);
  const handleSortClick = (event: React.MouseEvent<HTMLButtonElement>) => setSortAnchorEl(event.currentTarget);
  const handleSortClose = () => setSortAnchorEl(null);

  const handleCategoryChange = (event: SelectChangeEvent<string[]>) => {
    const { value } = event.target;
    setSelectedCategories(typeof value === 'string' ? value.split(',') : value);
    setCurrentPage(1);
  };

  const handlePriceChange = (_event: Event, newValue: number | number[]) => {
    if (Array.isArray(newValue)) {
      setPriceRange(newValue as [number, number]);
    }
  };

  const handleSortChange = (sortValue: string) => {
    setSortBy(sortValue);
    setSortAnchorEl(null);
    setCurrentPage(1);
  };

  const handleViewModeChange = (_event: React.MouseEvent<HTMLElement>, newMode: 'grid' | 'list' | null) => {
    if (newMode !== null) setViewMode(newMode);
  };

  const handleMigration = async () => {
    setIsMigrating(true);
    setMigrationResult(null);
    try {
      const result = await syncAllPublishedTemplates();
      setMigrationResult(result);
      // Refresh products after migration
      const data = await getPublishedProducts();
      const processedData = data.map((p) => ({ ...p, createdAt: p.createdAt ? new Date(p.createdAt) : new Date() }));
      setProducts(processedData);
    } catch (error) {
      console.error('Migration failed:', error);
      setMigrationResult({ created: 0, errors: [String(error)] });
    } finally {
      setIsMigrating(false);
    }
  };

  // UI-derived state
  const activeFiltersCount = useMemo(() => {
    let count = 0;
    if (selectedCategories.length > 0) count++;
    if (priceRange[0] > priceRangeBounds[0] || priceRange[1] < priceRangeBounds[1]) count++;
    if (showNewOnly) count++;
    if (showDiscountsOnly) count++;
    return count;
  }, [selectedCategories, priceRange, priceRangeBounds, showNewOnly, showDiscountsOnly]);

  const sortLabel = useMemo(() => {
    switch (sortBy) {
      case 'price-low':
        return 'Price: Low to High';
      case 'price-high':
        return 'Price: High to Low';
      case 'name-asc':
        return 'Name: A to Z';
      case 'name-desc':
        return 'Name: Z to A';
      case 'rating-high':
        return 'Highest Rated';
      case 'newest':
        return 'Newest First';
      default:
        return 'Sort By';
    }
  }, [sortBy]);

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Migration Section */}
      {(products.length === 0 && !loading) && (
        <Paper elevation={1} sx={{ p: 3, mb: 3, borderRadius: 2, background: theme.palette.warning.light }}>
          <Stack direction="row" spacing={2} alignItems="center" justifyContent="space-between">
            <Box>
              <Typography variant="h6" gutterBottom>
                No Products Found
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Sync published templates to create products automatically
              </Typography>
            </Box>
            <Button
              variant="contained"
              color="primary"
              onClick={handleMigration}
              disabled={isMigrating}
              startIcon={isMigrating ? <RefreshIcon /> : undefined}
            >
              {isMigrating ? 'Syncing...' : 'Sync Templates'}
            </Button>
          </Stack>
          {migrationResult && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" color="success.main">
                ✅ Created {migrationResult.created} products
              </Typography>
              {migrationResult.errors.map((error, index) => (
                <Typography key={index} variant="body2" color="error.main">
                  ❌ {error}
                </Typography>
              ))}
            </Box>
          )}
        </Paper>
      )}

      {/* Top Bar */}
      <Paper elevation={0} sx={{ p: 2, mb: 3, borderRadius: 2, background: theme.palette.background.default }}>
        <Stack direction={isMobile ? 'column' : 'row'} spacing={2} alignItems="center">
          <TextField
            fullWidth
            variant="outlined"
            placeholder="Search products..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
              sx: { borderRadius: 1 }
            }}
          />
          <Stack direction="row" spacing={1} flexShrink={0}>
            <Tooltip title="Filters">
              <IconButton onClick={handleFilterClick}>
                <Badge badgeContent={activeFiltersCount} color="primary">
                  <TuneIcon />
                </Badge>
              </IconButton>
            </Tooltip>
            <Tooltip title="Sort">
              <Button startIcon={<SortIcon />} onClick={handleSortClick} variant="outlined">
                {isMobile ? '' : sortLabel}
              </Button>
            </Tooltip>
            <ToggleButtonGroup value={viewMode} exclusive onChange={handleViewModeChange} size="small">
              <ToggleButton value="grid">
                <GridViewIcon />
              </ToggleButton>
              <ToggleButton value="list">
                <ViewListIcon />
              </ToggleButton>
            </ToggleButtonGroup>
            <Tooltip title="Reset Filters">
              <IconButton onClick={resetFilters}>
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          </Stack>
        </Stack>
      </Paper>

      {/* Filter Popover */}
      <Popover
        open={openFilterPopover}
        anchorEl={filterAnchorEl}
        onClose={handleFilterClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        transformOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Box sx={{ p: 3, width: 320 }}>
          <Stack spacing={2}>
            <Typography variant="h6">Filters</Typography>
            <Divider />
            <FormControl fullWidth>
              <Typography variant="subtitle1" gutterBottom>
                Category
              </Typography>
              <Select
                multiple
                value={selectedCategories}
                onChange={handleCategoryChange}
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {(selected as string[]).map((value) => (
                      <Chip key={value} label={value} size="small" />
                    ))}
                  </Box>
                )}
              >
                {categories.map((category) => (
                  <MenuItem key={category} value={category}>
                    {category}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <Box>
              <Typography variant="subtitle1" gutterBottom>
                Price Range
              </Typography>
              <Slider
                value={priceRange}
                onChange={handlePriceChange}
                onChangeCommitted={() => setCurrentPage(1)} // Reset page when slider interaction ends
                valueLabelDisplay="auto"
                min={priceRangeBounds[0]}
                max={priceRangeBounds[1]}
                sx={{ mt: 3 }}
              />
            </Box>
            <Divider />
            <FormControlLabel
              control={
                <Checkbox
                  checked={showNewOnly}
                  onChange={(e) => {
                    setShowNewOnly(e.target.checked);
                    setCurrentPage(1);
                  }}
                />
              }
              label={
                <>
                  <NewReleasesIcon sx={{ mr: 1, verticalAlign: 'bottom' }} /> New Arrivals
                </>
              }
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={showDiscountsOnly}
                  onChange={(e) => {
                    setShowDiscountsOnly(e.target.checked);
                    setCurrentPage(1);
                  }}
                />
              }
              label={
                <>
                  <LocalOfferIcon sx={{ mr: 1, verticalAlign: 'bottom' }} /> Special Discounts
                </>
              }
            />
            <Divider />
            <Button onClick={handleFilterClose} variant="contained">
              Apply Filters
            </Button>
          </Stack>
        </Box>
      </Popover>

      {/* Sort Menu */}
      <Menu anchorEl={sortAnchorEl} open={openSortMenu} onClose={handleSortClose}>
        <MenuItem onClick={() => handleSortChange('default')} selected={sortBy === 'default'}>
          Default
        </MenuItem>
        <MenuItem onClick={() => handleSortChange('price-low')} selected={sortBy === 'price-low'}>
          Price: Low to High
        </MenuItem>
        <MenuItem onClick={() => handleSortChange('price-high')} selected={sortBy === 'price-high'}>
          Price: High to Low
        </MenuItem>
        <MenuItem onClick={() => handleSortChange('name-asc')} selected={sortBy === 'name-asc'}>
          Name: A-Z
        </MenuItem>
        <MenuItem onClick={() => handleSortChange('name-desc')} selected={sortBy === 'name-desc'}>
          Name: Z-A
        </MenuItem>
        <MenuItem onClick={() => handleSortChange('rating-high')} selected={sortBy === 'rating-high'}>
          Highest Rated
        </MenuItem>
        <MenuItem onClick={() => handleSortChange('newest')} selected={sortBy === 'newest'}>
          Newest First
        </MenuItem>
      </Menu>

      {/* Products Grid */}
      <Box sx={{ mt: 4 }}>
        {loading ? (
          <Grid container spacing={3}>
            {Array.from(new Array(itemsPerPage)).map((_, index) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <Skeleton variant="rectangular" height={320} />
              </Grid>
            ))}
          </Grid>
        ) : paginatedProducts.length > 0 ? (
          <Grid container spacing={3}>
            {paginatedProducts.map((product) => (
              <Grid item xs={12} sm={viewMode === 'grid' ? 6 : 12} md={viewMode === 'grid' ? 4 : 12} key={product.id}>
                <ProductCard product={product} viewMode={viewMode} />
              </Grid>
            ))}
          </Grid>
        ) : (
          <Card sx={{ mt: 4, textAlign: 'center', p: 4 }}>
            <CardContent>
              <Typography variant="h5">No Products Found</Typography>
              <Typography color="text.secondary" sx={{ mt: 1 }}>
                Try adjusting your search or filter criteria.
              </Typography>
              <Button variant="outlined" onClick={resetFilters} sx={{ mt: 2 }}>
                Reset Filters
              </Button>
            </CardContent>
          </Card>
        )}
      </Box>

      {/* Pagination */}
      {!loading && paginatedProducts.length > 0 && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
          <Pagination
            count={Math.ceil(sortedProducts.length / itemsPerPage)}
            page={currentPage}
            onChange={(_e, page) => setCurrentPage(page)}
            color="primary"
          />
        </Box>
      )}
    </Container>
  );
};

export default Products;
