/**
 * Firebase Module
 *
 * This module exports Firebase services for use throughout the application.
 */

// Export Firebase configuration and instances
export { auth, db, storage } from './[config]/firebase';

// Export Firebase utilities
export * from './[services]/firestoreUtils';

// Export collection-specific services
export * as UsersService from './[services]/usersService';
export * as QuestionnairesService from './[services]/questionnairesService';
export * as QuestionnaireResponsesService from './[services]/questionnaireResponsesService';
export * as QuestionnaireTemplatesService from './[services]/questionnaireTemplatesService';
export * as ClinicsService from './[services]/clinicsService';
export * as ReportsService from './[services]/reportsService';
export * as ComplianceReportsService from './[services]/complianceReportsService';
export * as PurchasesService from './[services]/purchasesService';
export * as ProductReviewsService from './[services]/productReviewsService';

// Legacy service exports (kept for backward compatibility)
export * as FirestoreService from './[services]/firestore';
export * as StorageService from './[services]/storage';
