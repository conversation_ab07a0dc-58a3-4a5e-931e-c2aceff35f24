/**
 * Application Routes
 *
 * This file serves as the single source of truth for all routes in the application.
 * It provides constants for route paths and utility functions to access and build routes.
 * Using this centralized approach ensures consistency in route structure and makes
 * route management more reliable.
 */

import { Role } from 'RBAC/[types]/Role';

// Base path constants
const TRQ_BASE = '/trq';

// Authentication routes
const AUTH = {
  LOGIN: `${TRQ_BASE}/login`,
  REGISTER: `${TRQ_BASE}/register`,
  FORGOT_PASSWORD: `${TRQ_BASE}/forgot-password`,
  RESET_PASSWORD: `${TRQ_BASE}/reset-password`,
  UNAUTHORIZED: `${TRQ_BASE}/unauthorized`,
  LOGOUT: `/logout`
};

// Role-specific dashboard routes
const HOMEPAGES = {
  ADMIN: `${TRQ_BASE}/admin/home`,
  CLINIC_ADMIN: `${TRQ_BASE}/clinic-admins/home`,
  DOCTOR: `${TRQ_BASE}/doctors/home`,
  CLIENT: `${TRQ_BASE}/clients/home`,
  PATIENT: `${TRQ_BASE}/patients/home`
};

// User routes
const USERS = {
  LIST: `${TRQ_BASE}/users`,
  DETAILS: (id: string) => `${TRQ_BASE}/users/${id}/details`,
  EDIT: (id: string) => `${TRQ_BASE}/users/${id}/edit`,
  ADD: `${TRQ_BASE}/users/add`,
  // Pattern routes for React Router
  DETAILS_PATTERN: `${TRQ_BASE}/users/:id`,
  EDIT_PATTERN: `${TRQ_BASE}/users/:id/edit`,
  MIGRATION: `${TRQ_BASE}/users/migration`,
  LIST_PATTERN: `${TRQ_BASE}/users`
};

// Patient routes
const PATIENTS = {
  HOME: HOMEPAGES.PATIENT,
  LIST: `${TRQ_BASE}/patients`,
  DETAILS: (id: string) => `${TRQ_BASE}/patients/${id}/details`,
  EDIT: (id: string) => `${TRQ_BASE}/patients/${id}/edit`,
  PROFILE: (id: string) => `${TRQ_BASE}/patients/${id}/profile`,
  ADD: `${TRQ_BASE}/patients/add`,
  MY_PATIENTS: `${TRQ_BASE}/patients/my-patients`,
  // Pattern routes for React Router
  DETAILS_PATTERN: `${TRQ_BASE}/patients/:id/details`,
  EDIT_PATTERN: `${TRQ_BASE}/patients/:id/edit`,
  PROFILE_PATTERN: `${TRQ_BASE}/patients/:id/profile`,
  ADD_PATTERN: `${TRQ_BASE}/patients/add`,
  LIST_PATTERN: `${TRQ_BASE}/patients`
};

// Client routes
const CLIENTS = {
  HOME: HOMEPAGES.CLIENT,
  LIST: `${TRQ_BASE}/clients`,
  DETAILS: (id: string) => `${TRQ_BASE}/clients/${id}/details`,
  EDIT: (id: string) => `${TRQ_BASE}/clients/${id}/edit`,
  PROFILE: (id: string) => `${TRQ_BASE}/clients/${id}/profile`,
  ADD: `${TRQ_BASE}/clients/add`,
  ADD_PATIENT: `${TRQ_BASE}/clients/add-patient`,
  BILLING: `${TRQ_BASE}/clients/billing`,
  // Pattern routes for React Router
  DETAILS_PATTERN: `${TRQ_BASE}/clients/:id/details`,
  EDIT_PATTERN: `${TRQ_BASE}/clients/:id/edit`,
  PROFILE_PATTERN: `${TRQ_BASE}/clients/:id/profile`,
  ADD_PATTERN: `${TRQ_BASE}/clients/add`,
  LIST_PATTERN: `${TRQ_BASE}/clients`
};

// Doctor routes
const DOCTORS = {
  HOME: HOMEPAGES.DOCTOR,
  LIST: `${TRQ_BASE}/doctors`,
  DETAILS: (id: string) => `${TRQ_BASE}/doctors/${id}/details`,
  EDIT: (id: string) => `${TRQ_BASE}/doctors/${id}/edit`,
  PROFILE: (id: string) => `${TRQ_BASE}/doctors/${id}/profile`,
  ADD: `${TRQ_BASE}/doctors/add`,
  MY_PATIENTS: `${TRQ_BASE}/doctors/my-patients`,
  MY_DOCTOR: `${TRQ_BASE}/doctors/my-doctor`,
  // Pattern routes for React Router
  DETAILS_PATTERN: `${TRQ_BASE}/doctors/:id/details`,
  EDIT_PATTERN: `${TRQ_BASE}/doctors/:id/edit`,
  PROFILE_PATTERN: `${TRQ_BASE}/doctors/:id/profile`,
  ADD_PATTERN: `${TRQ_BASE}/doctors/add`,
  LIST_PATTERN: `${TRQ_BASE}/doctors`
};

// Clinic admin routes
const CLINIC_ADMINS = {
  HOME: HOMEPAGES.CLINIC_ADMIN,
  LIST: `${TRQ_BASE}/clinic-admins`,
  DETAILS: (id: string) => `${TRQ_BASE}/clinic-admins/${id}/details`,
  EDIT: (id: string) => `${TRQ_BASE}/clinic-admins/${id}/edit`,
  PROFILE: `${TRQ_BASE}/clinic-admins/profile`,
  ADD: `${TRQ_BASE}/clinic-admins/add`,
  // Pattern routes for React Router
  DETAILS_PATTERN: `${TRQ_BASE}/clinic-admins/:id/details`,
  EDIT_PATTERN: `${TRQ_BASE}/clinic-admins/:id/edit`,
  ADD_PATTERN: `${TRQ_BASE}/clinic-admins/add`,
  LIST_PATTERN: `${TRQ_BASE}/clinic-admins`
};

// Clinic routes
const CLINICS = {
  LIST: `${TRQ_BASE}/clinics`,
  DETAILS: (id: string) => `${TRQ_BASE}/clinics/${id}/details`,
  EDIT: (id: string) => `${TRQ_BASE}/clinics/${id}/edit`,
  ADD: `${TRQ_BASE}/clinics/add`,
  PROFILE: `${TRQ_BASE}/clinics/profile`,
  MY_CLINIC: `${TRQ_BASE}/clinics/my-clinic`,
  // Pattern routes for React Router
  DETAILS_PATTERN: `${TRQ_BASE}/clinics/:id/details`,
  EDIT_PATTERN: `${TRQ_BASE}/clinics/:id/edit`,
  ADD_PATTERN: `${TRQ_BASE}/clinics/add`,
  PROFILE_PATTERN: `${TRQ_BASE}/clinics/profile`,
  LIST_PATTERN: `${TRQ_BASE}/clinics`
};

// Questionnaire routes
const QUESTIONNAIRES = {
  LIST: `${TRQ_BASE}/questionnaires`,
  ADD: `${TRQ_BASE}/questionnaires/add`,
  MY_QUESTIONNAIRES: `${TRQ_BASE}/questionnaires/my-questionnaires`,
  DETAILS: (id: string) => `${TRQ_BASE}/questionnaires/${id}/details`,
  WIZARD: (id: string) => `${TRQ_BASE}/questionnaires/wizard/${id}`,
  RESPIRATORY: `${TRQ_BASE}/questionnaires/respiratory`,
  EDIT: (id: string) => `${TRQ_BASE}/questionnaires/${id}/edit`,
  REVIEW: (id: string) => `${TRQ_BASE}/questionnaires/review/${id}`,
  // Pattern routes for React Router
  DETAILS_PATTERN: `${TRQ_BASE}/questionnaires/:id/details`,
  WIZARD_PATTERN: `${TRQ_BASE}/questionnaires/wizard/:id`,
  RESPIRATORY_PATTERN: `${TRQ_BASE}/questionnaires/respiratory`,
  LIST_PATTERN: `${TRQ_BASE}/questionnaires`,
  ADD_PATTERN: `${TRQ_BASE}/questionnaires/add`,
  EDIT_PATTERN: `${TRQ_BASE}/questionnaires/:id/edit`,
  REVIEW_PATTERN: `${TRQ_BASE}/questionnaires/review/:id`
};

// Template routes
const TEMPLATES = {
  LIST: `${TRQ_BASE}/templates`,
  DETAILS: (id: string) => `${TRQ_BASE}/templates/${id}/details`,
  EDIT: (id: string) => `${TRQ_BASE}/templates/${id}/edit`,
  ADD: `${TRQ_BASE}/templates/add`,
  // Pattern routes for React Router
  DETAILS_PATTERN: `${TRQ_BASE}/templates/:id/details`,
  EDIT_PATTERN: `${TRQ_BASE}/templates/:id/edit`,
  ADD_PATTERN: `${TRQ_BASE}/templates/add`,
  LIST_PATTERN: `${TRQ_BASE}/templates`
};

// Compliance report routes
const COMPLIANCE_REPORTS = {
  LIST: `${TRQ_BASE}/compliance-reports`,
  DETAILS: (id: string) => `${TRQ_BASE}/compliance-reports/${id}/details`,
  EDIT: (id: string) => `${TRQ_BASE}/compliance-reports/${id}/edit`,
  REVIEW: (id: string) => `${TRQ_BASE}/compliance-reports/${id}/review`,
  // Pattern routes for React Router
  DETAILS_PATTERN: `${TRQ_BASE}/compliance-reports/:id/details`,
  EDIT_PATTERN: `${TRQ_BASE}/compliance-reports/:id/edit`,
  REVIEW_PATTERN: `${TRQ_BASE}/compliance-reports/:id/review`,
  LIST_PATTERN: `${TRQ_BASE}/compliance-reports`
};

// Admin routes
const ADMIN = {
  HOME: HOMEPAGES.ADMIN,
  ROLES: `${TRQ_BASE}/admin/roles`,
  PERMISSIONS: `${TRQ_BASE}/admin/permissions`,
  DASHBOARD: `${TRQ_BASE}/admin/dashboard`,
  ONBOARDING: `${TRQ_BASE}/admin/onboarding`
};

// Other routes
const OTHERS = {
  ANALYTICS: `${TRQ_BASE}/analytics`,
  SETTINGS: `${TRQ_BASE}/settings`,
  HEALTH_INSIGHTS: `${TRQ_BASE}/health-insights`,
  APPOINTMENTS: `${TRQ_BASE}/appointments`,
  MESSAGES: `${TRQ_BASE}/messages`,
  MEDICAL_RECORDS: `${TRQ_BASE}/medical-records`,
  REPORTS: `${TRQ_BASE}/reports`,
  HOME: `${TRQ_BASE}/home`
};

// Error routes
const ERRORS = {
  NOT_FOUND: `${TRQ_BASE}/404`,
  ERROR: `${TRQ_BASE}/error`,
  ERROR_500: `${TRQ_BASE}/error-500`,
  UNDER_CONSTRUCTION: `${TRQ_BASE}/under-construction`
};

const PRODUCTS = {
  LIST: `${TRQ_BASE}/products`,
  ADD: `${TRQ_BASE}/products/add`,
  EDIT: (id: string) => `${TRQ_BASE}/products/${id}/edit`,
  DETAILS: (id: string) => `${TRQ_BASE}/products/${id}/details`,
  // Pattern routes for React Router
  DETAILS_PATTERN: `${TRQ_BASE}/products/:id/details`,
  EDIT_PATTERN: `${TRQ_BASE}/products/:id/edit`,
  ADD_PATTERN: `${TRQ_BASE}/products/add`,
  LIST_PATTERN: `${TRQ_BASE}/products`
};

const AI_CHAT = {
  BASE: `${TRQ_BASE}/ai-chat`
};

const PURCHASES = {
  LIST: `${TRQ_BASE}/purchases`,
  CART: `${TRQ_BASE}/purchases/cart`,
  CART_CHECKOUT: `${TRQ_BASE}/purchases/cart/checkout`,
  CHECKOUT: `${TRQ_BASE}/purchases/checkout`,
  MY_PURCHASES: `${TRQ_BASE}/purchases/my-purchases`,
  DETAILS: (id: string) => `${TRQ_BASE}/purchases/${id}/details`,
  EDIT: (id: string) => `${TRQ_BASE}/purchases/${id}/edit`,
  ADD: `${TRQ_BASE}/purchases/add`,
  // Pattern routes for React Router
  DETAILS_PATTERN: `${TRQ_BASE}/purchases/:id/details`,
  EDIT_PATTERN: `${TRQ_BASE}/purchases/:id/edit`,
  ADD_PATTERN: `${TRQ_BASE}/purchases/add`,
  LIST_PATTERN: `${TRQ_BASE}/purchases`
};

const REPORTS = {
  LIST: `${TRQ_BASE}/reports`,
  DETAILS: (id: string) => `${TRQ_BASE}/reports/${id}/details`,
  REVIEW: (id: string) => `${TRQ_BASE}/reports/${id}/review`,
  // Pattern routes for React Router
  ADD_PATTERN: `${TRQ_BASE}/reports/add`,
  LIST_PATTERN: `${TRQ_BASE}/reports`,
  REVIEW_PATTERN: `${TRQ_BASE}/reports/:id/review`,
  DETAILS_PATTERN: `${TRQ_BASE}/reports/:id/details`
};

// Debug and development tools
const DEBUG = {
  SCREENSHOT_VIEWER: `${TRQ_BASE}/debug/screenshot-viewer`,
  UI_AUDIT: `${TRQ_BASE}/debug/ui-audit`
};

// Messaging routes
const MESSAGING = {
  LIST: `${TRQ_BASE}/messages`,
  CHAT: (chatId: string) => `${TRQ_BASE}/messages/${chatId}`,
  LIST_PATTERN: `${TRQ_BASE}/messages`,
  CHAT_PATTERN: `${TRQ_BASE}/messages/:chatId`
};

// Real-time Chat routes
const CHAT = {
  LIST: `${TRQ_BASE}/chat`,
  CONVERSATION: (conversationId: string) => `${TRQ_BASE}/chat/${conversationId}`,
  LIST_PATTERN: `${TRQ_BASE}/chat`,
  CONVERSATION_PATTERN: `${TRQ_BASE}/chat/:conversationId`
};

// Notification routes
const NOTIFICATIONS = {
  CENTER: `${TRQ_BASE}/notifications`,
  SETTINGS: `${TRQ_BASE}/notifications/settings`,
  CENTER_PATTERN: `${TRQ_BASE}/notifications`,
  SETTINGS_PATTERN: `${TRQ_BASE}/notifications/settings`
};

// Linear integration routes
const LINEAR = {
  DASHBOARD: `${TRQ_BASE}/linear/dashboard`,
  PROJECTS: `${TRQ_BASE}/linear/projects`,
  PROJECT_DETAILS: (id: string) => `${TRQ_BASE}/linear/projects/${id}`,
  ISSUES: `${TRQ_BASE}/linear/issues`,
  ISSUE_DETAILS: (id: string) => `${TRQ_BASE}/linear/issues/${id}`,
  SETTINGS: `${TRQ_BASE}/linear/settings`,
  // Pattern routes for React Router
  PROJECT_DETAILS_PATTERN: `${TRQ_BASE}/linear/projects/:id`,
  ISSUE_DETAILS_PATTERN: `${TRQ_BASE}/linear/issues/:id`,
  PROJECTS_PATTERN: `${TRQ_BASE}/linear/projects`,
  ISSUES_PATTERN: `${TRQ_BASE}/linear/issues`,
  DASHBOARD_PATTERN: `${TRQ_BASE}/linear/dashboard`,
  SETTINGS_PATTERN: `${TRQ_BASE}/linear/settings`
};

/**
 * Get the dashboard URL for a specific role
 * @param role The user role
 * @returns The dashboard URL
 */
export function getDashboardUrlForRole(role: Role | null | undefined): string {
  console.log('=== getDashboardUrlForRole DEBUG ===');
  console.log('Input role:', role);
  console.log('Input role type:', typeof role);
  console.log('Role enum values:', Object.values(Role));
  console.log('Role.Admin:', Role.Admin);
  console.log('Role.Client:', Role.Client);
  console.log('Role.Doctor:', Role.Doctor);
  console.log('Role.Patient:', Role.Patient);
  console.log('Role.ClinicAdmin:', Role.ClinicAdmin);

  if (!role) {
    console.error('No role provided to getDashboardUrlForRole');
    return AUTH.LOGIN;
  }

  let result: string;
  switch (role) {
    case Role.Admin:
      result = HOMEPAGES.ADMIN;
      console.log('Matched Role.Admin, returning:', result);
      break;
    case Role.ClinicAdmin:
      result = HOMEPAGES.CLINIC_ADMIN;
      console.log('Matched Role.ClinicAdmin, returning:', result);
      break;
    case Role.Doctor:
      result = HOMEPAGES.DOCTOR;
      console.log('Matched Role.Doctor, returning:', result);
      break;
    case Role.Client:
      result = HOMEPAGES.CLIENT;
      console.log('Matched Role.Client, returning:', result);
      break;
    case Role.Patient:
      result = HOMEPAGES.PATIENT;
      console.log('Matched Role.Patient, returning:', result);
      break;
    default:
      result = AUTH.LOGIN;
      console.warn('No role match found, defaulting to login. Role was:', role);
      break;
  }

  console.log('getDashboardUrlForRole final result:', result);
  return result;
}

/**
 * Helper function to resolve contextual IDs in routes
 * This function provides a way to handle both static and dynamic route parts
 *
 * @param routeFunc A function that takes an ID and returns a route string
 * @param contextId The ID to use in the route, or a placeholder if actual ID is not available
 * @returns A route string with the ID substituted
 */
export function resolveContextualRoute(routeFunc: (id: string) => string, contextId: string | null | undefined): string {
  // If contextId is null or undefined, use the pattern route
  if (contextId === null || contextId === undefined) {
    return routeFunc(':id');
  }

  // Otherwise, use the actual ID
  return routeFunc(contextId);
}

// Analytics routes (assuming a base path)
const ANALYTICS = {
  BASE: `${TRQ_BASE}/analytics`,
  // Add more specific analytics routes if needed, e.g.,
  // OVERVIEW: `${TRQ_BASE}/analytics/overview`,
  // USER_ACTIVITY: `${TRQ_BASE}/analytics/user-activity`,
  // Pattern routes for React Router
  BASE_PATTERN: `${TRQ_BASE}/analytics`
};

// Export all route objects
export const ROUTES = {
  TRQ_BASE,
  AUTH,
  HOMEPAGES,
  USERS,
  PATIENTS,
  CLIENTS,
  DOCTORS,
  CLINIC_ADMINS,
  CLINICS,
  QUESTIONNAIRES,
  TEMPLATES,
  COMPLIANCE_REPORTS,
  ADMIN,
  OTHERS,
  ERRORS,
  PRODUCTS,
  AI_CHAT,
  PURCHASES,
  ANALYTICS,
  REPORTS,
  MESSAGING,
  DEBUG,
  CHAT,
  NOTIFICATIONS,
  LINEAR
};

export default ROUTES;
