import { lazy } from 'react';
import Loadable from '[components]/Loadable';
import { Navigate } from 'react-router-dom';

// Purchases
const AllPurchases = Loadable(lazy(() => import('./[pages]/AllPurchases')));
const MyPurchases = Loadable(lazy(() => import('./[pages]/MyPurchases')));
const PurchaseDetails = Loadable(lazy(() => import('./[pages]/PurchaseDetails')));
const PurchaseCheckout = Loadable(lazy(() => import('./[pages]/PurchaseCheckout')));
const Checkout = Loadable(lazy(() => import('./[pages]/Checkout')));
const Cart = Loadable(lazy(() => import('./Cart/[pages]/Cart')));
const CartCheckout = Loadable(lazy(() => import('./Cart/[pages]/CartCheckout')));

const purchaseRoutes = {
  path: 'purchases',
  children: [
    { path: '', element: <AllPurchases /> },
    { path: 'my-purchases', element: <MyPurchases /> },
    { path: ':id', element: <PurchaseDetails /> },
    { path: 'details', element: <Navigate to="/trq/purchases" replace /> }, // Redirect if no ID
    { path: ':id/checkout', element: <PurchaseCheckout /> },
    { path: 'checkout', element: <Checkout /> },
    { path: 'cart', element: <Cart /> },
    { path: 'cart/checkout', element: <CartCheckout /> }
  ]
};

export default purchaseRoutes;
