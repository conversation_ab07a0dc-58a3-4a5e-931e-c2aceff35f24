import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useIntl } from 'react-intl';

// material-ui
import { Box, Button, Card, Grid, Stack, TextField, useTheme, MenuItem, Typography } from '@mui/material';
import { DataGrid, GridColDef, GridRowSelectionModel, GridRenderCellParams, GridToolbarContainer } from '@mui/x-data-grid';

// project imports
import MainCard from '[components]/cards/MainCard';
import { gridSpacing } from '[constants]/gridSpacing';
import useDataGrid from '[hooks]/useDataGrid';
import { Purchase, PaymentStatus } from '../[types]/Purchase'; // Corrected path

// icons
import VisibilityTwoToneIcon from '@mui/icons-material/VisibilityTwoTone';
import SearchIcon from '@mui/icons-material/Search';

// services
import { getPurchasesByClient } from '../[services]/purchaseService';

const AllPurchases = () => {
  const intl = useIntl();
  const navigate = useNavigate();
  const theme = useTheme();
  const dataGridStyles = useDataGrid();

  const [purchases, setPurchases] = useState<Purchase[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedRows, setSelectedRows] = useState<string[]>([]);

  useEffect(() => {
    const fetchPurchases = async () => {
      try {
        setLoading(true);
        // For now, we'll fetch all purchases by using an empty client ID
        // This is a temporary solution until a proper getAllPurchases is implemented
        const purchasesData = await getPurchasesByClient('all-clients');
        setPurchases(purchasesData);
      } catch (error) {
        console.error('Error fetching purchases:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPurchases();
  }, []);

  const handleViewDetails = (id: string) => {
    navigate(`/trq/purchases/${id}`);
  };

  const handleSelectionChange = (rowSelectionModel: GridRowSelectionModel) => {
    setSelectedRows(rowSelectionModel.map((id) => id.toString()));
  };

  const filteredPurchases = purchases.filter((purchase) => {
    const matchesSearch =
      purchase.clientName.toLowerCase().includes(searchTerm.toLowerCase()) || purchase.id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || purchase.paymentStatus === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const columns: GridColDef[] = [
    {
      field: 'clientName',
      headerName: intl.formatMessage({ id: 'client-name' }) || 'Client Name',
      flex: 1,
      minWidth: 200
    },
    {
      field: 'totalQuantity',
      headerName: intl.formatMessage({ id: 'total-quantity' }) || 'Total Quantity',
      width: 150,
      align: 'right'
    },
    {
      field: 'totalPrice',
      headerName: intl.formatMessage({ id: 'total-price' }) || 'Total Price',
      width: 150,
      align: 'right',
      valueFormatter: (params: { value: number | null }) => {
        if (params.value == null) return '';
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD'
        }).format(params.value);
      }
    },
    {
      field: 'paymentStatus',
      headerName: intl.formatMessage({ id: 'payment-status' }) || 'Payment Status',
      width: 150,
      renderCell: (params: GridRenderCellParams<Purchase>) => {
        const status = params.row.paymentStatus;
        if (!status) return null;

        const statusColors = {
          [PaymentStatus.Pending]: {
            bg: theme.palette.info.light,
            color: theme.palette.info.dark
          },
          [PaymentStatus.Completed]: {
            bg: theme.palette.success.light,
            color: theme.palette.success.dark
          },
          [PaymentStatus.Failed]: {
            bg: theme.palette.error.light,
            color: theme.palette.error.dark
          },
          [PaymentStatus.Refunded]: {
            bg: theme.palette.warning.light,
            color: theme.palette.warning.dark
          }
        };

        return (
          <Box
            sx={{
              backgroundColor: statusColors[status].bg,
              color: statusColors[status].color,
              borderRadius: '16px',
              px: 2,
              py: 0.5,
              textTransform: 'capitalize'
            }}
          >
            {status}
          </Box>
        );
      }
    },
    {
      field: 'purchaseDate',
      headerName: intl.formatMessage({ id: 'purchase-date' }) || 'Purchase Date',
      width: 200,
      type: 'date',
      valueFormatter: (params: { value: string | Date | null }) => {
        if (params.value == null) return '';
        const date = new Date(params.value);
        if (isNaN(date.getTime())) return '';

        return date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
      }
    }
  ];

  // Custom toolbar
  const CustomToolbar = () => {
    return (
      <GridToolbarContainer>
        <Box display="flex" justifyContent="space-between" width="100%" alignItems="center" p={2}>
          <Typography variant="h3">{intl.formatMessage({ id: 'purchases' }) || 'Purchases'}</Typography>
          <Box sx={{ marginLeft: 'auto' }}>
            {selectedRows.length === 1 && (
              <Button
                color="primary"
                variant="outlined"
                startIcon={<VisibilityTwoToneIcon />}
                onClick={() => handleViewDetails(selectedRows[0])}
              >
                {intl.formatMessage({ id: 'view' }) || 'View'}
              </Button>
            )}
          </Box>
        </Box>
      </GridToolbarContainer>
    );
  };

  return (
    <MainCard title={intl.formatMessage({ id: 'purchases' }) || 'Purchases'}>
      <Grid container spacing={gridSpacing}>
        <Grid item xs={12}>
          <Card>
            <Box sx={{ p: 2 }}>
              <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} alignItems="center" justifyContent="space-between">
                <Stack direction="row" spacing={2} flex={1}>
                  <TextField
                    size="small"
                    placeholder={intl.formatMessage({ id: 'search-purchases' }) || 'Search purchases...'}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    InputProps={{
                      startAdornment: <SearchIcon sx={{ color: 'action.active', mr: 1 }} />
                    }}
                    sx={{ minWidth: 200 }}
                  />
                  <TextField
                    select
                    size="small"
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    sx={{ minWidth: 150 }}
                  >
                    <MenuItem value="all">{intl.formatMessage({ id: 'all-statuses' }) || 'All Statuses'}</MenuItem>
                    {Object.values(PaymentStatus).map((status) => (
                      <MenuItem key={status} value={status}>
                        {status.charAt(0).toUpperCase() + status.slice(1)}
                      </MenuItem>
                    ))}
                  </TextField>
                </Stack>
              </Stack>
            </Box>
            <Box sx={{ height: 'calc(100vh - 380px)', width: '100%' }}>
              <DataGrid
                rows={filteredPurchases.map((p) => ({
                  ...p,
                  purchaseDate: p.purchaseDate?.toDate ? p.purchaseDate.toDate() : new Date(p.purchaseDate as any)
                }))}
                columns={columns}
                loading={loading}
                checkboxSelection
                disableRowSelectionOnClick
                onRowSelectionModelChange={handleSelectionChange}
                sx={dataGridStyles}
                slots={{
                  toolbar: CustomToolbar
                }}
              />
            </Box>
          </Card>
        </Grid>
      </Grid>
    </MainCard>
  );
};

export default AllPurchases;
